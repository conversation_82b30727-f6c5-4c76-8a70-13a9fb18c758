import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/model/others/billed_item_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';

class AddEditSaleBillItemController extends GetxController {
  final String tag = "AddEditSaleBillItemController";
  UnitListController unitListController = Get.find();

  ItemRepository itemRepository = new ItemRepository();

  var _billedItem = LineItemDetailModel().obs;
  var _editFlag = false.obs;

  LineItemDetailModel get billedItem => _billedItem.value;

  bool get editFlag => _editFlag.value;
  final formKey = GlobalKey<FormState>();

  TextEditingController itemNameCtrl = TextEditingController();
  var selectedItem = ItemModel().obs;
  TextEditingController qtyCtrl = TextEditingController();
  TextEditingController rateCtrl = TextEditingController();
  TextEditingController grossAmountCtrl = TextEditingController();
  TextEditingController discountPercentageCtrl = TextEditingController();
  TextEditingController discountAmountCtrl = TextEditingController();
  TextEditingController netAmountCtrl = TextEditingController();
  BilledItemUnit selectedUnit = new BilledItemUnit();
  List<BilledItemUnit> unitList = [];

  List<BilledItemUnit> unitAllList = [];

  @override
  void onInit() async {
    super.onInit();
    // Initialize default values to prevent NaN
    discountPercentageCtrl.text = "0.00";
    discountAmountCtrl.text = "0.00";
    netAmountCtrl.text = "0.00";
    grossAmountCtrl.text = "0.00";
  }

  @override
  void dispose() {
    itemNameCtrl.dispose();
    qtyCtrl.dispose();
    rateCtrl.dispose();
    grossAmountCtrl.dispose();
    discountPercentageCtrl.dispose();
    discountAmountCtrl.dispose();
    netAmountCtrl.dispose();

    super.dispose();
  }

  init() async {}

  // initEdit(LineItemDetailModel lineItem) async {
  //   _editFlag.value = true;

  //   ItemModel _item;
  //   if (null != lineItem.itemId) {
  //     _item = await itemRepository.getItemById(lineItem.itemId!);
  //   } else {
  //     _item = ItemModel(itemName: lineItem.itemName);
  //   }

  //   itemOnSelectHandler(lineItem.itemId,
  //       item: _item, unitID: lineItem.lineItemUnitId!);

  //   if (null != lineItem.lineItemUnitId)
  //     unitOnSelectHandler(lineItem.lineItemUnitId);

  //   qtyCtrl.text = lineItem.quantity.toString();
  //   rateCtrl.text = lineItem.pricePerUnit.toString();
  //   grossAmountCtrl.text = lineItem.grossAmount.toString();
  //   discountPercentageCtrl.text = lineItem.discountPercent.toString();
  //   discountAmountCtrl.text = lineItem.discountAmount.toString();
  //   netAmountCtrl.text = lineItem.totalAmount.toString();

  //   _billedItem.value = lineItem;
  // }
  initEdit(LineItemDetailModel lineItem) async {
    _editFlag.value = true;

    ItemModel? item;
    if (null != lineItem.itemId) {
      item = await itemRepository.getItemById(lineItem.itemId ?? "");
    } else {
      item = ItemModel(itemName: lineItem.itemName);
    }

    itemOnSelectHandler(lineItem.itemId,
        item: item, unitID: lineItem.lineItemUnitId ?? "");

    if (null != lineItem.lineItemUnitId) {
      unitOnSelectHandler(lineItem.lineItemUnitId ?? "0.00");
    }

    qtyCtrl.text = lineItem.quantity.toString();
    rateCtrl.text = "${(lineItem.pricePerUnit ?? "")}";
    grossAmountCtrl.text = lineItem.grossAmount.toString();
    discountPercentageCtrl.text = lineItem.discountPercent.toString();
    discountAmountCtrl.text = lineItem.discountAmount.toString();
    netAmountCtrl.text = lineItem.totalAmount.toString();

    _billedItem.value = lineItem;
  }

  // itemOnClearHandler() {
  //   itemNameCtrl.text = "";
  //   // selectedItem = null;
  // }

  // itemOnSelectHandler(itemID, {String? unitID, ItemModel? item}) {
  //   itemNameCtrl.text = item?.itemName ?? "";
  //   rateCtrl.text = item?.itemSaleUnitPrice?.toString() ?? "";

  //   //assigning name
  //   billedItem.itemName = item?.itemName ?? "";
  //   // Log.d("selected item ${item.toJson()}");

  //   unitList.clear();

  //   if (null != unitID) {
  //     UnitModel? baseUnit =
  //         unitListController.units.firstWhere((unit) => unit.unitId == unitID);
  //     if (null != baseUnit)
  //       unitList.add(new BilledItemUnit(
  //           unitId: baseUnit.unitId,
  //           unitName: baseUnit.unitName,
  //           unitShortName: baseUnit.unitShortName,
  //           conversionFactor: 0.00,
  //           unitType: "base"));

  //     unitOnSelectHandler(unitID);
  //   }

  //   if (null != item?.alternateUnitId) {
  //     UnitModel? altUnit = unitListController.units
  //         .firstWhere((unit) => unit.unitId == item!.alternateUnitId!);
  //     if (null != altUnit)
  //       unitList.add(new BilledItemUnit(
  //           unitId: altUnit.unitId,
  //           unitName: altUnit.unitName,
  //           unitShortName: altUnit.unitShortName,
  //           conversionFactor: item?.unitConversionFactor,
  //           unitType: "alt"));
  //   }
  // }
  itemOnSelectHandler(itemID, {String? unitID, required ItemModel item}) {
    itemNameCtrl.text = item.itemName ?? "";
    selectedItem.value = item;
    selectedItem.refresh();

    billedItem.itemName = item.itemName;
    unitList.clear();

    if (null != unitID) {
      // Find base unit
      UnitModel? baseUnit = unitListController.units.firstWhere(
        (unit) => unit.unitId == unitID,
        orElse: () => UnitModel(),
      );

      if (baseUnit.unitId != null) {
        unitList.add(BilledItemUnit(
            unitId: baseUnit.unitId,
            unitName: baseUnit.unitName,
            unitShortName: baseUnit.unitShortName,
            conversionFactor: 0.00,
            unitType: "base"));
        unitOnSelectHandler(unitID);
      }
    } else {
      // Create a Set to ensure unique unitIds
      final uniqueUnits = <String>{};
      unitList = unitListController.units.where((unit) {
        final isUnique = uniqueUnits.add(unit.unitId ?? '');
        return isUnique && unit.unitId != null;
      }).map((e) {
        return BilledItemUnit(
          unitId: e.unitId,
          unitName: e.unitName,
          unitShortName: e.unitShortName,
          conversionFactor: 0.0,
          unitType: "base",
        );
      }).toList();

      if (unitList.isNotEmpty) {
        unitOnSelectHandler(unitList.first.unitId!);
      }
    }

    // Add alternate unit if exists and different from base unit
    if (item.alternateUnitId != null &&
        !unitList.any((u) => u.unitId == item.alternateUnitId)) {
      try {
        UnitModel altUnit = unitListController.units
            .firstWhere((unit) => unit.unitId == item.alternateUnitId);
        unitList.add(BilledItemUnit(
            unitId: altUnit.unitId,
            unitName: altUnit.unitName,
            unitShortName: altUnit.unitShortName,
            conversionFactor: item.unitConversionFactor,
            unitType: "alt"));
      } catch (e) {
        debugPrint('Alternate unit not found: $e');
      }
    }
  }

  // unitOnSelectHandler(value) {
  //   selectedUnit = unitList.firstWhere((element) => element.unitId == value,
  //       orElse: () => BilledItemUnit());

  //   _billedItem.value.lineItemUnitId = value;
  //   _billedItem.value.lineItemUnitName = selectedUnit.unitShortName;
  //   _billedItem.value.lineItemUnitConversionFactor =
  //       selectedUnit.conversionFactor;

  //   _billedItem.refresh();

  //   // if ("alt" == selectedUnit.unitType) {
  //   //   if ((selectedItem.itemSaleUnitPrice ?? 0.00) > 0.00) {
  //   //     rateCtrl.text = (selectedItem.itemSaleUnitPrice! *
  //   //             selectedItem.unitConversionFactor!)
  //   //         .toStringAsFixed(2);
  //   //   } else {
  //   //     rateCtrl.text = "";
  //   //   }
  //   // }

  //   rateOnChangeHandler(rateCtrl.text);
  // }
  unitOnSelectHandler(String value) {
    selectedUnit = unitList.firstWhere((element) => element.unitId == value,
        orElse: () => BilledItemUnit());

    _billedItem.value.lineItemUnitId = selectedUnit.unitId;
    _billedItem.value.lineItemUnitName = selectedUnit.unitShortName;
    _billedItem.value.lineItemUnitConversionFactor =
        selectedUnit.conversionFactor;
    _billedItem.refresh();

    // _billedItem.refresh();
    if ("alt" == selectedUnit.unitType) {
      rateCtrl.text =
          ((selectedItem.value.itemPurchaseUnitPrice ?? 0.00) > 0.00)
              ? ((selectedItem.value.itemPurchaseUnitPrice ?? 0.00) *
                      (selectedItem.value.unitConversionFactor ?? 0.00))
                  .toStringAsFixed(2)
              : "";
    } else {
      rateCtrl.text = ((selectedItem.value.itemSaleUnitPrice ?? 0.00) > 0.00)
          ? (selectedItem.value.itemSaleUnitPrice ?? 0.00).toStringAsFixed(2)
          : "";
    }

    rateOnChangeHandler(rateCtrl.text);
  }

  sabkoMilaune({String? editorTag}) {
    if (editorTag == "gross_amt") {
      double discount = (billedItem.discountAmount ?? 0.00);
      double discountPer = (billedItem.discountPercent ?? 0.00);
      double netAmt = (billedItem.grossAmount! - discount);
      billedItem.discountAmount = discount;
      billedItem.discountPercent = discountPer;
      billedItem.totalAmount = netAmt;
      _billedItem.refresh();
      netAmountCtrl.text = netAmt.toStringAsFixed(2);
      double qty = (billedItem.quantity ?? 0.00);
      if (qty > 0) {
        double gross = (billedItem.grossAmount ?? 0.00);
        double pricePerUnit = gross / qty;
        billedItem.pricePerUnit = pricePerUnit;
        _billedItem.refresh();
        // rateCtrl.text = pricePerUnit.toStringAsFixed(2);
      }
    }
    if (editorTag == 'qty_amt') {
      double qty = (billedItem.quantity ?? 0.00);
      double rate = (billedItem.pricePerUnit ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      if (rate > 0.00) {
        gross = qty * rate;
        billedItem.grossAmount = gross;
        billedItem.totalAmount = gross;
        _billedItem.refresh();
        grossAmountCtrl.text = gross.toStringAsFixed(2);
        netAmountCtrl.text = gross.toStringAsFixed(2);
      }
    }
    if (editorTag == 'price_per_unit') {
      double price = (billedItem.pricePerUnit ?? 0.00);
      double qty = (billedItem.quantity ?? 0.00);
      double gross = price * qty;
      if (price > 0) {
        billedItem.grossAmount = gross;
        _billedItem.refresh();
        grossAmountCtrl.text = gross.toStringAsFixed(2);
      }
    }

    if (editorTag == 'dis_percent') {
      double disPer = (billedItem.discountPercent ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      double disAmt = gross * disPer * 0.01;
      billedItem.discountAmount = disAmt;
      discountAmountCtrl.text = disAmt.toStringAsFixed(2);
      double net = gross - disAmt;
      billedItem.totalAmount = net;
      netAmountCtrl.text = net.toStringAsFixed(2);
    }

    if (editorTag == 'dis_amt') {
      double disAmt = (billedItem.discountAmount ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      double dispercent = gross > 0 ? (disAmt / gross) * 100 : 0.00;
      billedItem.discountPercent = dispercent;
      discountPercentageCtrl.text = dispercent.toStringAsFixed(2);
      double net = gross - disAmt;
      billedItem.totalAmount = net;
      netAmountCtrl.text = net.toStringAsFixed(2);
    }

    if (billedItem.discountPercent != null) {
      double disPer = (billedItem.discountPercent ?? 0.00);
      double gross = (billedItem.grossAmount ?? 0.00);
      double disAmt = gross * disPer * 0.01;
      billedItem.discountAmount = disAmt;
      discountAmountCtrl.text = disAmt.toStringAsFixed(2);
      double net = gross - disAmt;
      billedItem.totalAmount = net;
      netAmountCtrl.text = net.toStringAsFixed(2);
    }
  }

  qtyOnChangeHandler(value) {
    billedItem.quantity = (parseDouble(value) ?? 0.00);

    sabkoMilaune(editorTag: 'qty_amt');
  }

  // rateOnChangeHandler(value) {
  //   billedItem.pricePerUnit = parseDouble(value);

  //   recalculateDataForItem(editorTAG: "rate");
  // }
  rateOnChangeHandler(value) {
    billedItem.pricePerUnit = (parseDouble(value) ?? 0.00);
    sabkoMilaune(editorTag: 'price_per_unit');
  }

  // amountOnChangeHandler(value) {
  //   billedItem.grossAmount = parseDouble(value);
  //   recalculateDataForItem(editorTAG: "amount");
  // }
  amountOnChangeHandler(value) {
    billedItem.grossAmount = (parseDouble(value) ?? 0.00);
    _billedItem.refresh();
    sabkoMilaune(editorTag: "gross_amt");
  }

  discountAmtonChangeHandler(val) {
    billedItem.discountAmount = (parseDouble(val) ?? 0.00);

    recalculateDataForItem(editorTAG: "discount-amt");
  }

  // discountPercentOnChangeHandler(value) {
  //   billedItem.discountPercent = parseDouble(value);
  //   recalculateDataForItem(editorTAG: "discount-percent");
  // }
  discountPercentOnChangeHandler(String value) {
    billedItem.discountPercent = (parseDouble(value) ?? 0.00);
    sabkoMilaune(editorTag: 'dis_percent');
  }

  onDiscountAmoutChange(val) {
    double disAmt = (parseDouble(val) ?? 0.00);
    billedItem.discountAmount = disAmt;
    sabkoMilaune(editorTag: 'dis_amt');
  }

  recalculateDataForItem({String? editorTAG}) {
    Log.d("editorTag1 ${editorTAG}");

    try {
      if (editorTAG == "rate" || editorTAG == "qty") {
        billedItem.grossAmount =
            (billedItem.quantity ?? 0.00) * (billedItem.pricePerUnit ?? 0.00);
        grossAmountCtrl.text =
            (billedItem.grossAmount ?? 0.00).toStringAsFixed(2);
      }

      if (editorTAG == "amount") {
        if (rateCtrl.text.isEmpty) {
          double qty = (billedItem.quantity ?? 0.00);
          if (qty > 0.00) {
            billedItem.pricePerUnit = (billedItem.grossAmount ?? 0.00) / qty;
            rateCtrl.text =
                (billedItem.pricePerUnit ?? 0.00).toStringAsFixed(2);
          }
        }
      }

      billedItem.discountAmount = (billedItem.grossAmount! *
              parseDouble(discountPercentageCtrl.text)!) /
          100;
      discountAmountCtrl.text =
          (billedItem.discountAmount ?? 0.0).toStringAsFixed(2);

      billedItem.totalAmount =
          billedItem.grossAmount! - billedItem.discountAmount!;
      netAmountCtrl.text = (billedItem.totalAmount ?? 0.0).toStringAsFixed(2);
      if (editorTAG == "discount-amt") {}

      Log.d("billedItem ${billedItem.toJson()}");
    } catch (e, trace) {
      Log.e(tag, e.toString() + trace.toString());
    }
  }
}
